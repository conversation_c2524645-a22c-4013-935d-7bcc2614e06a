using Amazon.Runtime.Internal;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using RealEstate.Application.Common;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Notification;
using RealEstate.Application.DTO.Wallet;
using RealEstate.Application.Interfaces;
using RealEstate.Application.Services;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using Shared.Enums;
using Shared.Extensions;
using Shared.Helper;
using Shared.Results;
using System.Linq;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Infrastructure.Services
{
    public class PropertyService : IPropertyService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IWalletService _walletService;
        private readonly ILogger<PropertyService> _logger;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly IUserDashboardService _userDashboardService;
        private readonly IYezDataService _yezDataService;

        public PropertyService(IUnitOfWork unitOfWork, IMapper mapper,
            IWalletService walletService,
            ILogger<PropertyService> logger,
            IServiceScopeFactory scopeFactory,
            IUserDashboardService userDashboardService,
            IYezDataService yezDataService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _walletService = walletService;
            _logger = logger;
            _scopeFactory = scopeFactory;
            _userDashboardService = userDashboardService;
            _yezDataService = yezDataService;
        }

        public async Task<Result<PropertyDto>> GetPropertyByIdAsync(Guid id, bool asNoTracking = true)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id, asNoTracking, p => p.PropertyMedia, p => p.Owner);
            if (property == null)
            {
                return Result<PropertyDto>.Failure($"Không tìm thấy bất động sản với ID {id}.", ErrorType.NotFound);
            }
            return Result<PropertyDto>.Success(_mapper.Map<PropertyDto>(property));
        }

        public async Task<Result<PropertyDto>> GetPropertyBySlugAsync(string slug, bool asNoTracking = true)
        {
            var property = await _unitOfWork.Properties.GetPropertyBySlugAsync(slug);
            if (property == null)
            {
                return Result<PropertyDto>.Failure($"Không tìm thấy bất động sản với slug '{slug}'.", ErrorType.NotFound);
            }

            // Load related data if needed
            if (!asNoTracking)
            {
                property = await _unitOfWork.Properties.GetByIdAsync(property.Id, asNoTracking, p => p.PropertyMedia, p => p.Owner);
            }
            else
            {
                // For read-only operations, we can load the property with includes
                property = await _unitOfWork.Properties.FindOneAsync(
                    p => p.Slug == slug && !p.IsDeleted,
                    asNoTracking,
                    null,
                    p => p.PropertyMedia,
                    p => p.Owner);
            }

            if (property == null)
            {
                return Result<PropertyDto>.Failure($"Không tìm thấy bất động sản với slug '{slug}'.", ErrorType.NotFound);
            }

            var userAvatar = await _unitOfWork.UserAvatars.FindOneAsync(p => p.UserID == property.OwnerID);

            var propertyDto = _mapper.Map<PropertyDto>(property);

            // Set the avatar URL from userAvatar if it exists
            if (userAvatar != null && propertyDto.Owner != null)
            {
                propertyDto.Owner.AvatarURL = userAvatar.MediaURL;
            }

            return Result<PropertyDto>.Success(propertyDto);
        }

        public async Task<Result<IEnumerable<PropertyDto>>> GetPropertyByUserAsync(Guid userId)
        {
            var properties = await _unitOfWork.Properties.FindAsync(p => p.OwnerID == userId);
            return Result<IEnumerable<PropertyDto>>.Success(_mapper.Map<IEnumerable<PropertyDto>>(properties));
        }

        public async Task<Result<PagedResultDto<PropertyDto>>> GetPropertyByUserWithStatusAsync(Guid userId, List<PropertyStatus> statuses, int page = 1, int pageSize = 10)
        {
            var now = DateTime.UtcNow;
            var query = _unitOfWork.Properties.GetQueryable()
                .Include(p => p.PropertyMedia)
                .Where(p => p.OwnerID == userId && !p.IsDeleted);

            if (statuses != null && statuses.Any())
            {
                // Apply status filter considering expiration
                query = query.Where(p => statuses.Contains(p.ExpiresAt <= now ? PropertyStatus.Expired : p.Status));
            }

            var totalCount = await query.CountAsync();
            var pageCount = (int)Math.Ceiling(totalCount / (double)pageSize);
            if (page < 1) page = 1;
            else if (page > pageCount && pageCount > 0) page = pageCount;

            var items = await query
                .OrderByDescending(p => p.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var itemDtos = _mapper.Map<List<PropertyDto>>(items);

            var result = new PagedResultDto<PropertyDto>
            {
                Items = itemDtos,
                TotalCount = totalCount,
                PageCount = pageCount,
                CurrentPage = page,
                PageSize = pageSize
            };
            return Result<PagedResultDto<PropertyDto>>.Success(result);
        }

        public async Task<Result<PropertyDto>> CreatePropertyAsync(CreatePropertyDto propertyDto, Guid userId)
        {
            // Determine listing price and duration
            ListingPriceDto selectedListingPrice;
            if (propertyDto.ListingDays.HasValue)
            {
                selectedListingPrice = await _yezDataService.GetListingPriceByDaysAsync(propertyDto.ListingDays.Value);
                if (selectedListingPrice == null)
                {
                    return Result<PropertyDto>.Failure($"Không tìm thấy gói đăng tin cho {propertyDto.ListingDays.Value} ngày.", ErrorType.Validation);
                }
            }
            else
            {
                selectedListingPrice = await _yezDataService.GetDefaultListingPriceAsync();
                if (selectedListingPrice == null)
                {
                    // Fallback to hardcoded values if no default is set
                    selectedListingPrice = new ListingPriceDto
                    {
                        Days = 10,
                        Price = POST_FEE,
                        IsDefault = true
                    };
                }
            }

            if (propertyDto.Status == PropertyStatus.PendingApproval)
            {
                var highlightFeeResult = await _userDashboardService.GetHighlightFeeByUserIdAsync(userId);
                var totalFee = selectedListingPrice.Price + (propertyDto.IsHighlighted ? highlightFeeResult.Value : 0);
                var walletBalanceResult = await _walletService.GetBalanceAsync(userId);
                if (!walletBalanceResult.IsSuccess || walletBalanceResult.Value == null || walletBalanceResult.Value.Balance < totalFee) // Ensure Value is not null
                {
                    return Result<PropertyDto>.Failure($"Số dư ví không đủ. Cần {totalFee:N0} VND để đăng bài viết.", ErrorType.Validation);
                }
            }

            var property = _mapper.Map<Property>(propertyDto);
            property.OwnerID = propertyDto.OwnerId ?? userId;
            property.UpdateRemainingTimes = UPDATE_DEFAULT_REMAINING_TIMES;
            property.Status = propertyDto.Status;
            property.CreatedBy = userId;
            property.CreatedAt = DateTime.UtcNow;
            property.Slug = propertyDto.Name.ToSlug();

            // Set PostPrice, ExpiresAt, and PaidDays based on selected listing price
            property.PostPrice = selectedListingPrice.Price;
            property.ExpiresAt = DateTime.UtcNow.AddDays(selectedListingPrice.Days);
            property.PaidDays = selectedListingPrice.Days;

            await _unitOfWork.Properties.AddAsync(property);

            var updateStatusLog = new PropertyStatusLog
            {
                PropertyID = property.Id,
                ChangedBy = userId,
                ChangedAt = DateTime.UtcNow,
                Status = property.Status,
                Comment = "Tạo tin đăng thành công",
            };

            await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);
            await _unitOfWork.SaveChangesAsync();

            if (property.Status == PropertyStatus.PendingApproval)
            {
                // Send notification for property owner            
                _ = Task.Run(async () =>
                {
                    using (var scope = _scopeFactory.CreateScope())
                    {
                        try
                        {
                            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();
                            var userService = scope.ServiceProvider.GetRequiredService<IUserService>();

                            var userResult = await userService.GetUserByIdAsync(userId, false);
                            if (userResult.IsSuccess)
                            {
                                var user = userResult.Value;
                                var actionUrl = Helper.ConstructActionUrl(NotificationType.ListingCreated, property.Id);
                                var request = new NotificationRequest
                                {
                                    TargetChannels = NotificationChannel.Email | NotificationChannel.InApp,
                                    InAppNotificationType = NotificationType.ListingCreated,
                                    RecipientId = propertyDto.OwnerId ?? userId,
                                    RecipientEmail = user.Email,
                                    EmailType = EmailType.PendingApproval.ToString(),
                                    Title = $"Đăng bài thành công.",
                                    Message = $"bài đăng bất động sản của bạn với tiêu đề {property.Name} hiện đang ở trạng thái **đang chờ duyệt**. Bạn có thể xem lại bài đăng trong phần [Bất động sản]",
                                    Data = new Dictionary<string, string>
                                    {
                                    { "user_name", user.FullName },
                                    { "listing_title", property.Name },
                                    { "propertyCode", property.Code.ToString() },
                                    { "propertyId", property.Id.ToString() },
                                    { "actionUrl", actionUrl }
                                    }
                                };

                                // Gửi đi
                                await notificationService.SendAsync(request);
                            }
                        }
                        catch (Exception ex)
                        {
                            var logger = scope.ServiceProvider.GetRequiredService<ILogger<ContactRequestService>>();
                            logger.LogError(ex, $"Lỗi khi gửi notification trong tác vụ nền cho property {property.Id}");
                        }
                    }
                });
            }

            return Result<PropertyDto>.Success(_mapper.Map<PropertyDto>(property));
        }

        public async Task<Result<PropertyDto>> UpdatePropertyAsync(Guid id, CreatePropertyDto propertyDto, Guid userId)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null)
            {
                return Result<PropertyDto>.Failure($"Không tìm thấy bất động sản với ID {id}.", ErrorType.NotFound);
            }
            if (property.OwnerID != userId)
            {
                return Result<PropertyDto>.Failure("Người dùng không có quyền cập nhật bất động sản này.", ErrorType.Unauthorized);
            }

            // Handle ListingDays changes (similar to CreatePropertyAsync)
            if (propertyDto.ListingDays.HasValue)
            {
                var selectedListingPrice = await _yezDataService.GetListingPriceByDaysAsync(propertyDto.ListingDays.Value);
                if (selectedListingPrice == null)
                {
                    return Result<PropertyDto>.Failure($"Không tìm thấy gói đăng tin cho {propertyDto.ListingDays.Value} ngày.", ErrorType.Validation);
                }

                // Update pricing and duration only if ListingDays changed
                if (property.PaidDays != selectedListingPrice.Days)
                {
                    property.PostPrice = selectedListingPrice.Price;
                    property.PaidDays = selectedListingPrice.Days;
                    // Note: ExpiresAt will be recalculated when status changes to Approved
                }
            }

            _mapper.Map(propertyDto, property);
            property.UpdatedBy = userId;

            _unitOfWork.Properties.Update(property);

            var updateStatusLog = new PropertyStatusLog
            {
                PropertyID = id,
                ChangedBy = userId,
                ChangedAt = DateTime.UtcNow,
                Status = propertyDto.Status,
                Comment = "update thông tin của tin đăng",
            };

            await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);
            await _unitOfWork.SaveChangesAsync();

            return Result<PropertyDto>.Success(_mapper.Map<PropertyDto>(property));
        }

        public async Task<Result> DeletePropertyAsync(Guid id, Guid userId)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null)
            {
                return Result.Failure($"Không tìm thấy bất động sản với ID {id}.", ErrorType.NotFound);
            }
            if (property.OwnerID != userId)
            {
                return Result.Failure("Người dùng không có quyền xóa bất động sản này.", ErrorType.Unauthorized);
            }

            property.UpdatedBy = userId;
            _unitOfWork.Properties.Remove(property);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> DeletePropertiesAsync(List<Guid> ids, Guid userId)
        {
            if (ids == null || !ids.Any())
                return Result.Failure("Không có ID bất động sản nào được cung cấp.", ErrorType.Validation);

            var properties = await _unitOfWork.Properties.GetQueryable().Where(p => ids.Contains(p.Id)).ToListAsync();
            if (properties.Count != ids.Count)
            {
                return Result.Failure("Không tìm thấy một hoặc nhiều bất động sản.", ErrorType.NotFound);
            }
            if (properties.Any(p => p.OwnerID != userId))
            {
                return Result.Failure("Người dùng không có quyền xóa một hoặc nhiều bất động sản.", ErrorType.Unauthorized);
            }

            foreach (var property in properties)
            {
                property.UpdatedBy = userId;
                _unitOfWork.Properties.Remove(property);
            }

            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> UpdateStatusAsync(Guid id, Guid userId, UpdateStatusDto updateStatusDto)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null) return Result.Failure($"Không tìm thấy bất động sản với ID {id}.", ErrorType.NotFound);

            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
            {
                return Result.Failure("Không tìm thấy người dùng.", ErrorType.NotFound);
            }

            if (property.OwnerID != userId && user.UserType != UserType.Admin)
            {
                return Result.Failure("Người dùng không có quyền cập nhật bất động sản này.", ErrorType.Unauthorized);
            }

            if (updateStatusDto.Status == PropertyStatus.PendingApproval
                && property.UpdateRemainingTimes <= 0)
            {
                return Result.Failure("Không còn lần cập nhật nào.", ErrorType.Validation);
            }

            // Check wallet balance and process payment if status is being updated to Approved
            WalletTransactionDto? walletTransaction = null;
            if (updateStatusDto.Status == PropertyStatus.Approved)
            {
                // Calculate total fee using property's PostPrice (which was set based on ListingPrice)
                var highlightFeeResult = await _userDashboardService.GetHighlightFeeByUserIdAsync(property.OwnerID);
                var totalFee = property.PostPrice + (property.IsHighlighted ? highlightFeeResult.Value : 0);

                // Check if user has enough money in wallet
                var walletBalanceResult = await _walletService.GetBalanceAsync(property.OwnerID);
                if (!walletBalanceResult.IsSuccess || walletBalanceResult.Value == null)
                {
                    return Result.Failure("Không thể lấy thông tin số dư ví.", ErrorType.Validation);
                }

                if (walletBalanceResult.Value.Balance < totalFee)
                {
                    return Result.Failure($"Số dư ví không đủ. Cần: {totalFee:N0} VND, Có sẵn: {walletBalanceResult.Value.Balance:N0} VND", ErrorType.Validation);
                }

                // Process wallet payment
                var spendRequest = new SpendWalletDto
                {
                    Amount = totalFee,
                    PaymentType = TransactionType.SPEND_POST.ToString(),
                    PaymentMethod = "wallet",
                    Description = $"Phí duyệt bất động sản #{property.Code} - {property.Name}"
                };

                var walletResult = await _walletService.SpendAsync(property.OwnerID, spendRequest);
                if (!walletResult.IsSuccess)
                {
                    return Result.Failure($"Thanh toán thất bại: {walletResult.ErrorMessage}", ErrorType.PaymentFailed);
                }

                walletTransaction = walletResult.Value;
            }

            // check old status is reject and new status is pending approve =>
            // meaning user resend the property post for review again, should decrease the number of remaining resend review
            if (property.Status == PropertyStatus.RejectedByAdmin
                && updateStatusDto.Status == PropertyStatus.PendingApproval)
            {
                property.UpdateRemainingTimes = property.UpdateRemainingTimes - 1;
            }
            property.Status = updateStatusDto.Status;
            property.UpdatedBy = userId;

            // Recalculate ExpiresAt when status changes to Approved based on approval date + PaidDays
            if (updateStatusDto.Status == PropertyStatus.Approved)
            {
                property.ExpiresAt = DateTime.UtcNow.AddDays(property.PaidDays);
            }

            _unitOfWork.Properties.Update(property);

            var updateStatusLog = new PropertyStatusLog
            {
                PropertyID = id,
                ChangedBy = userId,
                ChangedAt = DateTime.UtcNow,
                Status = updateStatusDto.Status,
                Comment = updateStatusDto.Status == PropertyStatus.Sold ? "Giao dịch thành công" : updateStatusDto.Comment,
            };

            await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);

            // Create SpendingLog record if status was updated to Approved successfully
            if (updateStatusDto.Status == PropertyStatus.Approved && walletTransaction != null)
            {
                // Recalculate the total fee for logging (using property's PostPrice)
                var highlightFeeResult = await _userDashboardService.GetHighlightFeeByUserIdAsync(property.OwnerID);
                var totalFee = property.PostPrice + (property.IsHighlighted ? highlightFeeResult.Value : 0);

                var spendingLog = new SpendingLog
                {
                    PropertyId = property.Id,
                    UserId = property.OwnerID,
                    Amount = totalFee,
                    SpendingType = "approval",
                    TransactionId = walletTransaction.Id,
                    SpentAt = DateTime.UtcNow,
                    Details = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        PropertyCode = property.Code,
                        PropertyName = property.Name,
                        PostFee = property.PostPrice, // Use property's PostPrice instead of hardcoded POST_FEE
                        HighlightFee = property.IsHighlighted ? highlightFeeResult.Value : 0,
                        TotalApprovalFee = totalFee,
                        IsHighlighted = property.IsHighlighted,
                        PaymentMethod = "wallet"
                    })
                };
                await _unitOfWork.SpendingLogs.AddAsync(spendingLog);
            }

            await _unitOfWork.SaveChangesAsync();

            if (updateStatusDto.Status == PropertyStatus.PendingApproval
                || updateStatusDto.Status == PropertyStatus.Approved
                || updateStatusDto.Status == PropertyStatus.RejectedByAdmin)
            {
                // Send notification for property owner            
                _ = Task.Run(async () =>
                {
                    using (var scope = _scopeFactory.CreateScope())
                    {
                        try
                        {
                            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();
                            var userService = scope.ServiceProvider.GetRequiredService<IUserService>();

                            var userResult = await userService.GetUserByIdAsync(property.OwnerID, false);
                            if (userResult.IsSuccess)
                            {
                                var user = userResult.Value;
                                if (user == null) return;
                                var actionUrl = Helper.ConstructActionUrl(NotificationType.ListingCreated, property.Id);

                                var propertyStatus = updateStatusDto.Status;
                                var notificationType = NotificationMapper.PropertyStatusToNotificationType[propertyStatus];
                                var notificationTypeText = NotificationMapper.GetNotificationTypeText(notificationType);

                                var request = new NotificationRequest
                                {
                                    TargetChannels = NotificationChannel.Email | NotificationChannel.InApp,
                                    InAppNotificationType = notificationType,
                                    RecipientId = user.Id,
                                    RecipientEmail = user.Email,
                                    EmailType = property.Status.ToString(),
                                    Title = notificationTypeText,
                                    Message = $"bài đăng bất động sản của bạn với tiêu đề {property.Name} hiện đang ở trạng thái {notificationTypeText}. Bạn có thể xem lại bài đăng trong phần [Bất động sản]",
                                    Data = new Dictionary<string, string>
                                    {
                                    { "user_name", user.FullName },
                                    { "listing_title", property.Name },
                                    { "propertyCode", property.Code.ToString() },
                                    { "propertyId", property.Id.ToString() },
                                    { "actionUrl", actionUrl }
                                    }
                                };

                                // Gửi đi
                                await notificationService.SendAsync(request);
                            }
                        }
                        catch (Exception ex)
                        {
                            var logger = scope.ServiceProvider.GetRequiredService<ILogger<ContactRequestService>>();
                            logger.LogError(ex, $"Lỗi khi gửi notification trong tác vụ nền cho property {property.Id}");
                        }
                    }
                });
            }

            return Result.Success();
        }

        public async Task<Result> UpdateStatusBulkAsync(List<Guid> ids, Guid userId, UpdateStatusDto updateStatusDto)
        {
            if (ids == null || !ids.Any())
                return Result.Failure("Không có ID bất động sản nào được cung cấp.", ErrorType.Validation);

            var properties = await _unitOfWork.Properties.GetQueryable().Where(p => ids.Contains(p.Id)).ToListAsync();
            if (properties.Count != ids.Count)
            {
                return Result.Failure("Không tìm thấy một hoặc nhiều bất động sản.", ErrorType.NotFound);
            }

            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user.UserType != UserType.Admin)
            {
                if (properties.Any(p => p.OwnerID != userId))
                {
                    return Result.Failure("Người dùng không có quyền cập nhật một hoặc nhiều bất động sản.", ErrorType.Unauthorized);
                }
            }

            foreach (var property in properties)
            {
                property.Status = updateStatusDto.Status;
                property.UpdatedBy = userId;
                _unitOfWork.Properties.Update(property);

                var updateStatusLog = new PropertyStatusLog
                {
                    PropertyID = property.Id,
                    ChangedBy = userId,
                    ChangedAt = DateTime.UtcNow,
                    Status = updateStatusDto.Status,
                    Comment = updateStatusDto.Comment,
                };
                await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);
            }

            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result<PropertyDto>> UpdateHighlightWithPaymentAsync(Guid propertyId, Guid userId, bool isHighlighted)
        {
            try
            {
                // 1. Get property and validate basic requirements
                var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
                if (property == null)
                {
                    return Result<PropertyDto>.Failure($"Không tìm thấy bất động sản với ID {propertyId}.", ErrorType.NotFound);
                }

                if (property.OwnerID != userId)
                {
                    return Result<PropertyDto>.Failure("Người dùng không có quyền cập nhật bất động sản này.", ErrorType.Unauthorized);
                }

                // 2. Handle unhighlighting (isHighlighted = false)
                if (!isHighlighted)
                {
                    property.IsHighlighted = false;
                    await _unitOfWork.SaveChangesAsync();
                    return Result<PropertyDto>.Success(_mapper.Map<PropertyDto>(property));
                }

                // 3. Requirement 1: If property is already highlighted, return message
                if (property.IsHighlighted)
                {
                    return Result<PropertyDto>.Failure("Bất động sản đã được làm nổi bật.", ErrorType.Validation);
                }

                // 4. Requirement 2: Check property status - some statuses cannot be highlighted
                var invalidStatuses = new[] { PropertyStatus.Expired, PropertyStatus.Sold, PropertyStatus.WaitingPayment };
                if (invalidStatuses.Contains(property.Status))
                {
                    return Result<PropertyDto>.Failure($"Bất động sản có trạng thái '{property.Status}' không thể làm nổi bật.", ErrorType.Validation);
                }

                // 5. Requirement 3: For Draft, PendingApproval, RejectedByAdmin - highlight without payment
                var noPaymentStatuses = new[] { PropertyStatus.Draft, PropertyStatus.PendingApproval, PropertyStatus.RejectedByAdmin };
                if (noPaymentStatuses.Contains(property.Status))
                {
                    property.IsHighlighted = true;
                    _unitOfWork.Properties.Update(property);
                    await _unitOfWork.SaveChangesAsync();
                    return Result<PropertyDto>.Success(_mapper.Map<PropertyDto>(property));
                }

                // 6. Requirement 4: For Approved status - highlight with payment
                if (property.Status == PropertyStatus.Approved)
                {
                    return await ProcessHighlightWithPaymentAsync(property, userId);
                }

                // 7. Handle any other status (like RejectedDueToUnpaid)
                return Result<PropertyDto>.Failure($"Bất động sản có trạng thái '{property.Status}' không thể làm nổi bật vào lúc này.", ErrorType.Validation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi cập nhật nổi bật cho bất động sản {PropertyId} bởi người dùng {UserId}", propertyId, userId);
                return Result<PropertyDto>.Failure("Đã xảy ra lỗi khi cập nhật nổi bật bất động sản.", ErrorType.Internal);
            }
        }

        private async Task<Result<PropertyDto>> ProcessHighlightWithPaymentAsync(Property property, Guid userId)
        {
            await using var transaction = await _unitOfWork.BeginTransactionAsync();
            try
            {
                // 1. Get user's highlight fee based on member rank
                var highlightFeeResult = await _userDashboardService.GetHighlightFeeByUserIdAsync(userId);
                if (!highlightFeeResult.IsSuccess)
                {
                    await _unitOfWork.RollbackTransactionAsync(transaction);
                    return Result<PropertyDto>.Failure($"Không thể xác định phí nổi bật: {highlightFeeResult.ErrorMessage}", ErrorType.Internal);
                }

                var highlightFee = highlightFeeResult.Value;

                // 2. Create spend request
                var spendRequest = new SpendWalletDto
                {
                    Amount = highlightFee,
                    PaymentType = TransactionType.SPEND_HIGHLIGHT.ToString(),
                    PaymentMethod = "wallet",
                    Description = $"Làm nổi bật bất động sản #{property.Code} - {property.Name}"
                };

                // 3. Process wallet payment
                var walletResult = await _walletService.SpendAsync(userId, spendRequest);
                if (!walletResult.IsSuccess)
                {
                    await _unitOfWork.RollbackTransactionAsync(transaction);
                    return Result<PropertyDto>.Failure($"Thanh toán thất bại: {walletResult.ErrorMessage}", ErrorType.PaymentFailed);
                }

                // 4. Create SpendingLog record
                var spendingLog = new SpendingLog
                {
                    PropertyId = property.Id,
                    UserId = userId,
                    Amount = highlightFee,
                    SpendingType = "highlight",
                    TransactionId = walletResult.Value.Id,
                    SpentAt = DateTime.UtcNow,
                    Details = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        PropertyCode = property.Code,
                        PropertyName = property.Name,
                        HighlightFee = highlightFee,
                        PaymentMethod = "wallet"
                    })
                };
                await _unitOfWork.SpendingLogs.AddAsync(spendingLog);

                // 5. Update property highlight status
                property.IsHighlighted = true;
                _unitOfWork.Properties.Update(property);
                await _unitOfWork.SaveChangesAsync();

                // 5. Commit transaction
                await _unitOfWork.CommitTransactionAsync(transaction);

                _logger.LogInformation("Bất động sản {PropertyId} được làm nổi bật thành công cho người dùng {UserId} với phí {Fee}",
                    property.Id, userId, highlightFee);

                // 6. Send notification for successful service payment
                _ = Task.Run(async () =>
                {
                    using (var scope = _scopeFactory.CreateScope())
                    {
                        try
                        {
                            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();
                            var userService = scope.ServiceProvider.GetRequiredService<IUserService>();
                            var walletService = scope.ServiceProvider.GetRequiredService<IWalletService>();

                            var userResult = await userService.GetUserByIdAsync(userId, false);
                            if (userResult.IsSuccess)
                            {
                                var user = userResult.Value;
                                var walletBalanceResult = await walletService.GetBalanceAsync(userId);
                                var newBalance = walletBalanceResult.IsSuccess ? walletBalanceResult.Value.Balance : 0;

                                var actionUrl = Helper.ConstructActionUrl(NotificationType.ServicePaymentSuccess, property.Id);
                                var request = new NotificationRequest
                                {
                                    TargetChannels = NotificationChannel.Email | NotificationChannel.InApp,
                                    InAppNotificationType = NotificationType.ServicePaymentSuccess,
                                    RecipientId = userId,
                                    RecipientEmail = user.Email,
                                    EmailType = EmailType.ServicePaymentSuccess.ToString(),
                                    Title = "Thanh toán dịch vụ thành công",
                                    Message = $"Thanh toán dịch vụ highlight cho bài đăng '{property.Name}' đã thành công. Chi phí: {highlightFee:N0} VND.",
                                    Data = new Dictionary<string, string>
                                    {
                                        { "user_name", user.FullName },
                                        { "service_name", $"Highlight bài đăng - {property.Name}" },
                                        { "transaction_id", $"HIGHLIGHT-{property.Code}" },
                                        { "amount_paid", $"{highlightFee:N0} VND" },
                                        { "transaction_time", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss") },
                                        { "new_balance", $"{newBalance:N0} VND" },
                                        { "service_management_link", actionUrl },
                                        { "privacy_policy_link", "/privacy-policy" },
                                        { "terms_link", "/terms-of-service" },
                                        { "actionUrl", actionUrl }
                                    }
                                };

                                // Send notification
                                await notificationService.SendAsync(request);
                            }
                        }
                        catch (Exception ex)
                        {
                            var logger = scope.ServiceProvider.GetRequiredService<ILogger<PropertyService>>();
                            logger.LogError(ex, $"Lỗi khi gửi thông báo thanh toán nổi bật thành công cho bất động sản {property.Id}");
                        }
                    }
                });

                return Result<PropertyDto>.Success(_mapper.Map<PropertyDto>(property));
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync(transaction);
                _logger.LogError(ex, "Lỗi khi xử lý thanh toán nổi bật cho bất động sản {PropertyId} bởi người dùng {UserId}", property.Id, userId);
                return Result<PropertyDto>.Failure("Đã xảy ra lỗi khi xử lý thanh toán nổi bật.", ErrorType.Internal);
            }
        }

        public async Task<Result<BulkHighlightResultDto>> UpdateHighlightBulkWithPaymentAsync(List<Guid> propertyIds, Guid userId, bool isHighlighted)
        {
            try
            {
                // 1. Validate input
                if (propertyIds == null || !propertyIds.Any())
                {
                    return Result<BulkHighlightResultDto>.Failure("Danh sách ID bất động sản không thể trống.", ErrorType.Validation);
                }

                // 2. Get all properties and validate ownership
                var properties = await _unitOfWork.Properties.GetQueryable()
                    .Where(p => propertyIds.Contains(p.Id) && !p.IsDeleted)
                    .ToListAsync();

                var result = new BulkHighlightResultDto
                {
                    TotalProcessed = propertyIds.Count,
                    PropertyResults = new List<PropertyHighlightResultDto>()
                };

                // Check for missing properties
                var foundPropertyIds = properties.Select(p => p.Id).ToList();
                var missingPropertyIds = propertyIds.Except(foundPropertyIds).ToList();

                foreach (var missingId in missingPropertyIds)
                {
                    result.PropertyResults.Add(new PropertyHighlightResultDto
                    {
                        PropertyId = missingId,
                        Success = false,
                        Status = "failed",
                        ErrorMessage = "Không tìm thấy bất động sản",
                        Cost = 0
                    });
                    result.Failed++;
                }

                // Check authorization for found properties
                var unauthorizedProperties = properties.Where(p => p.OwnerID != userId).ToList();
                foreach (var unauthorizedProperty in unauthorizedProperties)
                {
                    result.PropertyResults.Add(new PropertyHighlightResultDto
                    {
                        PropertyId = unauthorizedProperty.Id,
                        PropertyTitle = unauthorizedProperty.Name,
                        Success = false,
                        Status = "failed",
                        ErrorMessage = "Người dùng không có quyền cập nhật bất động sản này",
                        Cost = 0
                    });
                    result.Failed++;
                }

                // Get authorized properties only
                var authorizedProperties = properties.Where(p => p.OwnerID == userId).ToList();

                // 3. Handle unhighlighting case
                if (!isHighlighted)
                {
                    return await ProcessBulkUnhighlightAsync(authorizedProperties, result);
                }

                // 4. Process highlighting with business logic
                return await ProcessBulkHighlightAsync(authorizedProperties, userId, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi trong thao tác nổi bật hàng loạt cho người dùng {UserId}", userId);
                return Result<BulkHighlightResultDto>.Failure("Đã xảy ra lỗi trong thao tác nổi bật hàng loạt.", ErrorType.Internal);
            }
        }

        private async Task<Result<BulkHighlightResultDto>> ProcessBulkUnhighlightAsync(List<Property> properties, BulkHighlightResultDto result)
        {
            try
            {
                foreach (var property in properties)
                {
                    property.IsHighlighted = false;
                    result.PropertyResults.Add(new PropertyHighlightResultDto
                    {
                        PropertyId = property.Id,
                        PropertyTitle = property.Name,
                        Success = true,
                        Status = "unhighlighted",
                        ErrorMessage = null,
                        Cost = 0,
                        IsHighlighted = false
                    });
                    result.SuccessfullyHighlighted++;
                }

                await _unitOfWork.SaveChangesAsync();
                result.Success = true;
                result.Message = $"Đã bỏ nổi bật thành công {result.SuccessfullyHighlighted} bất động sản";

                return Result<BulkHighlightResultDto>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi trong thao tác bỏ nổi bật hàng loạt");
                return Result<BulkHighlightResultDto>.Failure("Đã xảy ra lỗi trong thao tác bỏ nổi bật hàng loạt.", ErrorType.Internal);
            }
        }

        private async Task<Result<BulkHighlightResultDto>> ProcessBulkHighlightAsync(List<Property> properties, Guid userId, BulkHighlightResultDto result)
        {
            try
            {
                // Step 1: Categorize properties and validate
                var propertiesToHighlight = new List<Property>();
                var propertiesRequiringPayment = new List<Property>();
                decimal totalCost = 0;

                foreach (var property in properties)
                {
                    // Apply same validation logic as individual highlight
                    var validationResult = ValidatePropertyForHighlight(property);
                    if (!validationResult.IsValid)
                    {
                        result.PropertyResults.Add(new PropertyHighlightResultDto
                        {
                            PropertyId = property.Id,
                            PropertyTitle = property.Name,
                            Success = false,
                            Status = validationResult.Status,
                            ErrorMessage = validationResult.ErrorMessage,
                            Cost = 0
                        });

                        if (validationResult.Status == "already_highlighted")
                            result.AlreadyHighlighted++;
                        else
                            result.Failed++;
                        continue;
                    }

                    propertiesToHighlight.Add(property);

                    // Check if payment is required (Approved status)
                    if (property.Status == PropertyStatus.Approved)
                    {
                        propertiesRequiringPayment.Add(property);
                    }
                }

                // Step 2: Calculate total cost for properties requiring payment
                if (propertiesRequiringPayment.Any())
                {
                    var highlightFeeResult = await _userDashboardService.GetHighlightFeeByUserIdAsync(userId);
                    if (!highlightFeeResult.IsSuccess)
                    {
                        return Result<BulkHighlightResultDto>.Failure($"Không thể xác định phí nổi bật: {highlightFeeResult.ErrorMessage}", ErrorType.Internal);
                    }

                    var highlightFee = highlightFeeResult.Value;
                    totalCost = highlightFee * propertiesRequiringPayment.Count;
                    result.TotalCost = totalCost;

                    // Validate wallet balance
                    var walletBalanceResult = await _walletService.GetBalanceAsync(userId);
                    if (!walletBalanceResult.IsSuccess || walletBalanceResult.Value.Balance < totalCost)
                    {
                        return Result<BulkHighlightResultDto>.Failure($"Số dư ví không đủ. Cần: {totalCost:N0} VND", ErrorType.PaymentFailed);
                    }
                }

                // Step 3: Process all properties in transaction
                return await ProcessPropertiesInTransactionAsync(propertiesToHighlight, propertiesRequiringPayment, userId, totalCost, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi trong xử lý nổi bật hàng loạt cho người dùng {UserId}", userId);
                return Result<BulkHighlightResultDto>.Failure("Đã xảy ra lỗi trong quá trình xử lý nổi bật hàng loạt.", ErrorType.Internal);
            }
        }

        private (bool IsValid, string Status, string ErrorMessage) ValidatePropertyForHighlight(Property property)
        {
            // Same validation logic as individual highlight
            if (property.IsHighlighted)
            {
                return (false, "already_highlighted", "Bất động sản đã được làm nổi bật");
            }

            var invalidStatuses = new[] { PropertyStatus.Expired, PropertyStatus.Sold, PropertyStatus.WaitingPayment };
            if (invalidStatuses.Contains(property.Status))
            {
                return (false, "failed", $"Bất động sản có trạng thái '{property.Status}' không thể làm nổi bật");
            }

            return (true, "valid", null);
        }

        private async Task<Result<BulkHighlightResultDto>> ProcessPropertiesInTransactionAsync(
            List<Property> propertiesToHighlight,
            List<Property> propertiesRequiringPayment,
            Guid userId,
            decimal totalCost,
            BulkHighlightResultDto result)
        {
            await using var transaction = await _unitOfWork.BeginTransactionAsync();
            try
            {
                // Process payment if required
                if (propertiesRequiringPayment.Any() && totalCost > 0)
                {
                    var highlightFeeResult = await _userDashboardService.GetHighlightFeeByUserIdAsync(userId);
                    var highlightFee = highlightFeeResult.Value;

                    // Create description with property codes
                    var propertyCodes = propertiesRequiringPayment.Select(p => p.Code.ToString()).ToList();
                    var propertyCodesText = string.Join(", ", propertyCodes);
                    var description = propertiesRequiringPayment.Count == 1
                        ? $"Làm nổi bật bất động sản #{propertyCodesText} - {propertiesRequiringPayment.First().Name}"
                        : $"Làm nổi bật hàng loạt {propertiesRequiringPayment.Count} bất động sản (#{propertyCodesText})";

                    var spendRequest = new SpendWalletDto
                    {
                        Amount = totalCost,
                        PaymentType = TransactionType.SPEND_HIGHLIGHT.ToString(),
                        PaymentMethod = "wallet",
                        Description = description
                    };

                    var walletResult = await _walletService.SpendAsync(userId, spendRequest);
                    if (!walletResult.IsSuccess)
                    {
                        await _unitOfWork.RollbackTransactionAsync(transaction);
                        return Result<BulkHighlightResultDto>.Failure($"Thanh toán thất bại: {walletResult.ErrorMessage}", ErrorType.PaymentFailed);
                    }

                    // Create SpendingLog records for properties requiring payment
                    foreach (var property in propertiesRequiringPayment)
                    {
                        var spendingLog = new SpendingLog
                        {
                            PropertyId = property.Id,
                            UserId = userId,
                            Amount = highlightFee,
                            SpendingType = "highlight",
                            TransactionId = walletResult.Value.Id,
                            SpentAt = DateTime.UtcNow,
                            Details = System.Text.Json.JsonSerializer.Serialize(new
                            {
                                PropertyCode = property.Code,
                                PropertyName = property.Name,
                                HighlightFee = highlightFee,
                                PaymentMethod = "wallet",
                                BulkOperation = true,
                                TotalPropertiesInBulk = propertiesRequiringPayment.Count
                            })
                        };
                        await _unitOfWork.SpendingLogs.AddAsync(spendingLog);
                    }
                }

                // Update all properties
                foreach (var property in propertiesToHighlight)
                {
                    property.IsHighlighted = true;
                    _unitOfWork.Properties.Update(property);

                    var cost = propertiesRequiringPayment.Contains(property) ?
                        (await _userDashboardService.GetHighlightFeeByUserIdAsync(userId)).Value : 0;

                    result.PropertyResults.Add(new PropertyHighlightResultDto
                    {
                        PropertyId = property.Id,
                        PropertyTitle = property.Name,
                        Success = true,
                        Status = "highlighted",
                        ErrorMessage = null,
                        Cost = cost,
                        IsHighlighted = true
                    });
                    result.SuccessfullyHighlighted++;
                }

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync(transaction);

                result.Success = true;
                result.Message = $"Đã làm nổi bật thành công {result.SuccessfullyHighlighted} bất động sản";
                result.TotalCost = totalCost;

                _logger.LogInformation("Nổi bật hàng loạt hoàn thành thành công cho người dùng {UserId}. Bất động sản: {Count}, Tổng chi phí: {Cost}",
                    userId, result.SuccessfullyHighlighted, totalCost);

                // Send notification for successful bulk service payment (only if payment was required)
                if (propertiesRequiringPayment.Any() && totalCost > 0)
                {
                    _ = Task.Run(async () =>
                    {
                        using (var scope = _scopeFactory.CreateScope())
                        {
                            try
                            {
                                var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();
                                var userService = scope.ServiceProvider.GetRequiredService<IUserService>();
                                var walletService = scope.ServiceProvider.GetRequiredService<IWalletService>();

                                var userResult = await userService.GetUserByIdAsync(userId, false);
                                if (userResult.IsSuccess)
                                {
                                    var user = userResult.Value;
                                    var walletBalanceResult = await walletService.GetBalanceAsync(userId);
                                    var newBalance = walletBalanceResult.IsSuccess ? walletBalanceResult.Value.Balance : 0;

                                    // Create service name for bulk highlight
                                    var propertyTitles = propertiesRequiringPayment.Take(3).Select(p => p.Name).ToList();
                                    var serviceName = propertiesRequiringPayment.Count == 1
                                        ? $"Nổi bật bài đăng - {propertyTitles.First()}"
                                        : $"Nổi bật hàng loạt {propertiesRequiringPayment.Count} bài đăng ({string.Join(", ", propertyTitles)}{(propertiesRequiringPayment.Count > 3 ? "..." : "")})";

                                    var actionUrl = Helper.ConstructActionUrl(NotificationType.ServicePaymentSuccess);
                                    var request = new NotificationRequest
                                    {
                                        TargetChannels = NotificationChannel.Email | NotificationChannel.InApp,
                                        InAppNotificationType = NotificationType.ServicePaymentSuccess,
                                        RecipientId = userId,
                                        RecipientEmail = user.Email,
                                        EmailType = EmailType.ServicePaymentSuccess.ToString(),
                                        Title = "Thanh toán dịch vụ thành công",
                                        Message = $"Thanh toán dịch vụ nổi bật cho {propertiesRequiringPayment.Count} bài đăng đã thành công. Chi phí: {totalCost:N0} VND.",
                                        Data = new Dictionary<string, string>
                                        {
                                            { "user_name", user.FullName },
                                            { "service_name", serviceName },
                                            { "transaction_id", $"NOIBAT-HANGLOT-{DateTime.Now:yyyyMMddHHmmss}" },
                                            { "amount_paid", $"{totalCost:N0} VND" },
                                            { "transaction_time", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss") },
                                            { "new_balance", $"{newBalance:N0} VND" },
                                            { "service_management_link", actionUrl },
                                            { "privacy_policy_link", "/privacy-policy" },
                                            { "terms_link", "/terms-of-service" },
                                            { "actionUrl", actionUrl }
                                        }
                                    };

                                    // Send notification
                                    await notificationService.SendAsync(request);
                                }
                            }
                            catch (Exception ex)
                            {
                                var logger = scope.ServiceProvider.GetRequiredService<ILogger<PropertyService>>();
                                logger.LogError(ex, $"Lỗi khi gửi thông báo thanh toán nổi bật hàng loạt thành công cho người dùng {userId}");
                            }
                        }
                    });
                }

                return Result<BulkHighlightResultDto>.Success(result);
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync(transaction);
                _logger.LogError(ex, "Lỗi trong giao dịch nổi bật hàng loạt cho người dùng {UserId}", userId);
                return Result<BulkHighlightResultDto>.Failure("Đã xảy ra lỗi trong giao dịch nổi bật hàng loạt.", ErrorType.Internal);
            }
        }

        public async Task<Result<PropertyDto>> RenewPropertyAsync(PropertyRenewalDto request, Guid userId)
        {
            var propertyResult = await GetPropertyByIdAsync(request.PropertyId);
            if (!propertyResult.IsSuccess || propertyResult.Value == null) // Ensure Value is not null
            {
                return Result<PropertyDto>.Failure("Không tìm thấy tin đăng cần gia hạn", ErrorType.NotFound);
            }

            var property = propertyResult.Value;

            if (property.OwnerId != userId)
            {
                return Result<PropertyDto>.Failure("Bạn không có quyền gia hạn tin đăng này", ErrorType.Unauthorized);
            }

            if (property.Status == PropertyStatus.Expired.ToString() || property.Status == PropertyStatus.Sold.ToString())
            {
                return Result<PropertyDto>.Failure("Không thể gia hạn bất động sản đã hết hạn hoặc đã bán", ErrorType.Validation);
            }

            if (!PropertyRenewalHelper.IsValidDuration(request.DurationInDays))
            {
                return Result<PropertyDto>.Failure($"Thời gian gia hạn không hợp lệ. Vui lòng chọn: {PropertyRenewalHelper.GetSupportedDurationsText()}", ErrorType.Validation);
            }

            // Get pricing using shared helper
            decimal totalFee;
            try
            {
                totalFee = PropertyRenewalHelper.GetPriceByDurationInDays(request.DurationInDays);
            }
            catch (ArgumentException ex)
            {
                return Result<PropertyDto>.Failure(ex.Message, ErrorType.Validation);
            }

            var walletBalanceResult = await _walletService.GetBalanceAsync(userId);
            if (!walletBalanceResult.IsSuccess || walletBalanceResult.Value == null || walletBalanceResult.Value.Balance < totalFee) // Ensure Value is not null
            {
                return Result<PropertyDto>.Failure($"Số dư ví không đủ. Cần {totalFee:N0} VND để gia hạn.", ErrorType.Validation);
            }

            var spendRequest = new SpendWalletDto
            {
                Amount = totalFee,
                PaymentType = TransactionType.SPEND_POST.ToString(),
                PaymentMethod = "wallet",
                Description = $"Gia hạn bất động sản #{property.Code} thêm {request.DurationInDays} ngày"
            };

            // Process wallet payment, property update, and logging in a single transaction
            WalletTransactionDto walletTransaction;
            Property propertyEntity;
            DateTimeOffset newExpiryDate;

            await using var transaction = await _unitOfWork.BeginTransactionAsync();
            try
            {
                // 1. Process wallet payment
                var transactionResult = await _walletService.SpendAsync(userId, spendRequest);
                if (!transactionResult.IsSuccess || transactionResult.Value == null)
                {
                    await _unitOfWork.RollbackTransactionAsync(transaction);
                    return Result<PropertyDto>.Failure("Giao dịch thanh toán không thành công: " + transactionResult.ErrorMessage, ErrorType.PaymentFailed);
                }
                walletTransaction = transactionResult.Value;

                // 2. Update property information
                propertyEntity = await _unitOfWork.Properties.GetByIdAsync(request.PropertyId);
                newExpiryDate = (propertyEntity.ExpiresAt > DateTime.UtcNow ? propertyEntity.ExpiresAt : DateTime.UtcNow).AddDays(request.DurationInDays);
                propertyEntity.ExpiresAt = newExpiryDate;
                if (propertyEntity.Status == PropertyStatus.Expired)
                {
                    propertyEntity.Status = PropertyStatus.Approved;
                }
                propertyEntity.RenewalCount++;
                propertyEntity.UpdatedBy = userId;

                // 3. Create SpendingLog entry
                var spendingLog = new SpendingLog
                {
                    PropertyId = propertyEntity.Id,
                    UserId = userId,
                    Amount = totalFee,
                    SpendingType = "renewal",
                    TransactionId = walletTransaction.Id,
                    SpentAt = DateTime.UtcNow,
                    Details = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        PropertyCode = propertyEntity.Code,
                        PropertyName = propertyEntity.Name,
                        DurationInDays = request.DurationInDays,
                        RenewalFee = totalFee,
                        NewExpiryDate = newExpiryDate.ToString("yyyy-MM-dd HH:mm:ss"),
                        PaymentMethod = "wallet"
                    })
                };
                await _unitOfWork.SpendingLogs.AddAsync(spendingLog);

                // 4. Save all changes and commit transaction
                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync(transaction);
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync(transaction);
                _logger.LogError(ex, $"Failed to renew property {request.PropertyId} for user {userId}");
                return Result<PropertyDto>.Failure("Có lỗi xảy ra trong quá trình gia hạn. Vui lòng thử lại.", ErrorType.Internal);
            }

            // Send notification in background task (outside transaction scope)
            if (_scopeFactory != null)
            {
                _ = Task.Run(async () =>
                {
                    using (var scope = _scopeFactory.CreateScope())
                    {
                        try
                        {
                            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();
                            var userService = scope.ServiceProvider.GetRequiredService<IUserService>();

                            var userResult = await userService.GetUserByIdAsync(userId, false);
                            if (userResult.IsSuccess)
                            {
                                var user = userResult.Value;
                                var actionUrl = Helper.ConstructActionUrl(NotificationType.ServicePaymentSuccess, propertyEntity.Id);
                                var newBalance = await _walletService.GetBalanceAsync(userId);

                                var notificationRequest = new NotificationRequest
                                {
                                    TargetChannels = NotificationChannel.Email | NotificationChannel.InApp,
                                    InAppNotificationType = NotificationType.ServicePaymentSuccess,
                                    RecipientId = userId,
                                    RecipientEmail = user.Email,
                                    EmailType = EmailType.PropertyRenewalSuccess.ToString(),
                                    Title = $"Gia hạn bất động sản thành công",
                                    Message = $"Bất động sản {propertyEntity.Name} đã được gia hạn thành công thêm {request.DurationInDays} ngày. Ngày hết hạn mới: {newExpiryDate:dd/MM/yyyy}",
                                    Data = new Dictionary<string, string>
                                    {
                                        { "user_name", user.FullName },
                                        { "listing_title", propertyEntity.Name },
                                        { "property_code", propertyEntity.Code.ToString() },
                                        { "duration_days", request.DurationInDays.ToString() },
                                        { "new_expiry_date", newExpiryDate.ToString("dd/MM/yyyy HH:mm") },
                                        { "transaction_id", walletTransaction.Id.ToString() },
                                        { "amount_paid", $"{totalFee:N0} VND" },
                                        { "transaction_time", DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss") },
                                        { "new_balance", newBalance.IsSuccess ? $"{newBalance.Value.Balance:N0} VND" : "N/A" },
                                        { "privacy_policy_link", "/privacy-policy" },
                                        { "terms_link", "/terms-of-service" },
                                        { "actionUrl", actionUrl }
                                    }
                                };

                                // Send notification
                                await notificationService.SendAsync(notificationRequest);
                            }
                        }
                        catch (Exception ex)
                        {
                            var logger = scope.ServiceProvider.GetRequiredService<ILogger<PropertyService>>();
                            logger.LogError(ex, $"Lỗi khi gửi thông báo gia hạn thành công cho bất động sản {propertyEntity.Id}");
                        }
                    }
                });
            }

            var updatedProperty = await GetPropertyByIdAsync(request.PropertyId);
            return updatedProperty;
        }

        public async Task<Result<int>> VerifyPropertyRemainingTimes(Guid propertyId)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
            if (property == null)
            {
                return Result<int>.Failure($"Không tìm thấy bất động sản với ID {propertyId}.", ErrorType.NotFound);
            }
            return Result<int>.Success(property.UpdateRemainingTimes ?? 0);
        }

        public async Task<Result<PagedResultDto<PropertyDto>>> SearchPropertiesAsync(PropertyFilterCriteriaDto filterCriteria)
        {
            // Start with a query that gets all non-deleted properties
            var query = _unitOfWork.Properties.GetQueryable()
                .Include(p => p.PropertyMedia)
                .Where(p => !p.IsDeleted);

            // Apply status filter - if no status specified, default to "Approved" for public search
            if (filterCriteria.Status != null && filterCriteria.Status.Any())
                query = query.Where(p => filterCriteria.Status.Contains(p.Status));

            // Apply filters
            if (filterCriteria.PostTypes != null && filterCriteria.PostTypes.Any())
                query = query.Where(p => filterCriteria.PostTypes.Contains(p.PostType));

            if (filterCriteria.PropertyTypes != null && filterCriteria.PropertyTypes.Any())
                query = query.Where(p => filterCriteria.PropertyTypes.Contains(p.PropertyType));

            //if (!string.IsNullOrEmpty(filterCriteria.CityId))
            //    query = query.Where(p => p.CityId == filterCriteria.CityId);

            //if (!string.IsNullOrEmpty(filterCriteria.DistrictId))
            //    query = query.Where(p => p.DistrictId == filterCriteria.DistrictId);

            if (!string.IsNullOrEmpty(filterCriteria.Address))
                query = query.Where(p => p.Address.Contains(filterCriteria.Address));

            if (filterCriteria.MinPrice.HasValue)
            {
                var minPrice = filterCriteria.MinPrice.Value * 1000000; // Convert to milion
                query = query.Where(p => p.Price >= minPrice);
            }

            if (filterCriteria.MaxPrice.HasValue)
            {
                var maxPrice = filterCriteria.MaxPrice.Value * 1000000; // Convert to milion
                query = query.Where(p => p.Price <= maxPrice);
            }

            if (filterCriteria.MinArea.HasValue)
                query = query.Where(p => p.Area >= filterCriteria.MinArea.Value);

            if (filterCriteria.MaxArea.HasValue)
                query = query.Where(p => p.Area <= filterCriteria.MaxArea.Value);

            if (filterCriteria.MinRooms.HasValue)
                query = query.Where(p => p.Rooms >= filterCriteria.MinRooms.Value);

            if (filterCriteria.MinToilets.HasValue)
                query = query.Where(p => p.Toilets >= filterCriteria.MinToilets.Value);

            if (!string.IsNullOrEmpty(filterCriteria.Direction))
                query = query.Where(p => p.Direction == filterCriteria.Direction);

            if (!string.IsNullOrEmpty(filterCriteria.Legality))
                query = query.Where(p => p.Legality == filterCriteria.Legality);

            if (filterCriteria.MinRoadWidth.HasValue)
                query = query.Where(p => p.RoadWidth >= filterCriteria.MinRoadWidth.Value);

            // Apply geographic filters if provided
            if (filterCriteria.SwLat.HasValue && filterCriteria.SwLng.HasValue && filterCriteria.NeLat.HasValue && filterCriteria.NeLng.HasValue)
            {
                query = query.Where(p =>
                    p.Latitude >= filterCriteria.SwLat.Value &&
                    p.Latitude <= filterCriteria.NeLat.Value &&
                    p.Longitude >= filterCriteria.SwLng.Value &&
                    p.Longitude <= filterCriteria.NeLng.Value);
            }

            //// Proximity search if coordinates and radius are provided
            //if (filterCriteria.Latitude.HasValue && filterCriteria.Longitude.HasValue && filterCriteria.Radius.HasValue)
            //{
            //    double earthRadiusKm = 6371; // Earth's radius in km

            //    // Convert latitude and longitude to radians BEFORE using them in the query
            //    double latRad = filterCriteria.Latitude.Value * Math.PI / 180.0;
            //    double lonRad = filterCriteria.Longitude.Value * Math.PI / 180.0;

            //    query = query.Where(l =>
            //                (earthRadiusKm * Math.Acos(
            //                    Math.Sin(latRad) * Math.Sin(Convert.ToDouble(l.Latitude) * Math.PI / 180.0) +
            //                    Math.Cos(latRad) * Math.Cos(Convert.ToDouble(l.Latitude) * Math.PI / 180.0) *
            //                    Math.Cos((Convert.ToDouble(l.Longitude) * Math.PI / 180.0) - lonRad)
            //                )) <= filterCriteria.Radius);
            //}

            // Count total items before pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var pageSize = filterCriteria.PageSize;
            var pageCount = (int)Math.Ceiling(totalCount / (double)pageSize);
            var currentPage = filterCriteria.Page;

            // Ensure current page is valid
            if (currentPage < 1)
                currentPage = 1;
            else if (currentPage > pageCount && pageCount > 0)
                currentPage = pageCount;

            // Apply pagination and get items
            var items = await query
                .OrderByDescending(n => n.CreatedAt)
                .Skip((currentPage - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Map to DTOs
            var itemDtos = _mapper.Map<List<PropertyDto>>(items);

            // Create and return paged result
            var result = new PagedResultDto<PropertyDto>
            {
                Items = itemDtos,
                TotalCount = totalCount,
                PageCount = pageCount,
                CurrentPage = currentPage,
                PageSize = pageSize
            };
            return Result<PagedResultDto<PropertyDto>>.Success(result);
        }

        public async Task<Result> UpdatePropertyRenewalAsync(UpdatePropertyRenewalDto updateDto, Guid userId)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(updateDto.PropertyId);
            if (property == null)
            {
                return Result.Failure($"Không tìm thấy bất động sản với ID {updateDto.PropertyId}.", ErrorType.NotFound);
            }

            property.ExpiresAt = updateDto.ExpiresAt;
            if (updateDto.IncrementRenewalCount)
            {
                property.RenewalCount++;
            }
            property.UpdatedBy = userId;

            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result<int>> CountPropertiesAsync(PropertyFilterCriteriaDto filterCriteria)
        {
            // Start with a query that gets all non-deleted properties
            var query = _unitOfWork.Properties.GetQueryable()
                .Where(p => !p.IsDeleted);

            // Apply status filter - if no status specified, default to "Approved" for public search
            if (filterCriteria.Status != null && filterCriteria.Status.Any())
                query = query.Where(p => filterCriteria.Status.Contains(p.Status));
            else
                query = query.Where(p => p.Status == PropertyStatus.Approved);

            // Apply filters
            if (filterCriteria.PostTypes != null && filterCriteria.PostTypes.Any())
                query = query.Where(p => filterCriteria.PostTypes.Contains(p.PostType));

            if (filterCriteria.PropertyTypes != null && filterCriteria.PropertyTypes.Any())
                query = query.Where(p => filterCriteria.PropertyTypes.Contains(p.PropertyType));

            if (!string.IsNullOrEmpty(filterCriteria.Address))
                query = query.Where(p => p.Address.Contains(filterCriteria.Address));

            if (filterCriteria.MinPrice.HasValue)
            {
                var minPrice = filterCriteria.MinPrice.Value * 1000000; // Convert to million
                query = query.Where(p => p.Price >= minPrice);
            }

            if (filterCriteria.MaxPrice.HasValue)
            {
                var maxPrice = filterCriteria.MaxPrice.Value * 1000000; // Convert to million
                query = query.Where(p => p.Price <= maxPrice);
            }

            if (filterCriteria.MinArea.HasValue)
                query = query.Where(p => p.Area >= filterCriteria.MinArea.Value);

            if (filterCriteria.MaxArea.HasValue)
                query = query.Where(p => p.Area <= filterCriteria.MaxArea.Value);

            if (filterCriteria.MinRooms.HasValue)
                query = query.Where(p => p.Rooms >= filterCriteria.MinRooms.Value);

            if (filterCriteria.MinToilets.HasValue)
                query = query.Where(p => p.Toilets >= filterCriteria.MinToilets.Value);

            if (!string.IsNullOrEmpty(filterCriteria.Direction))
                query = query.Where(p => p.Direction == filterCriteria.Direction);

            if (!string.IsNullOrEmpty(filterCriteria.Legality))
                query = query.Where(p => p.Legality == filterCriteria.Legality);

            if (filterCriteria.MinRoadWidth.HasValue)
                query = query.Where(p => p.RoadWidth >= filterCriteria.MinRoadWidth.Value);

            // Apply geographic bounds filter if provided
            if (filterCriteria.SwLat.HasValue && filterCriteria.SwLng.HasValue &&
                filterCriteria.NeLat.HasValue && filterCriteria.NeLng.HasValue)
            {
                query = query.Where(p =>
                    p.Latitude >= filterCriteria.SwLat.Value &&
                    p.Latitude <= filterCriteria.NeLat.Value &&
                    p.Longitude >= filterCriteria.SwLng.Value &&
                    p.Longitude <= filterCriteria.NeLng.Value);
            }

            var count = await query.CountAsync();
            return Result<int>.Success(count);
        }

        public async Task<Result<PropertyCountStatsDto>> GetPropertyCountByStatusAsync()
        {
            var now = DateTime.UtcNow;
            var stats = await _unitOfWork.Properties.GetQueryable()
                .Select(p => new {
                   EffectiveStatus = p.ExpiresAt <= now ? PropertyStatus.Expired : p.Status
                })
                .GroupBy(p => p.EffectiveStatus)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            var result = new PropertyCountStatsDto();
            foreach (var status in Enum.GetNames(typeof(PropertyStatus)))
            {
                result.PropertiesByStatus[status] = 0;
            }
            foreach (var item in stats)
            {
                result.PropertiesByStatus[item.Status.ToString()] = item.Count;
            }
            return Result<PropertyCountStatsDto>.Success(result);
        }

        public async Task<Result<PropertyCountStatsDto>> GetPropertyCountStatsByUserAsync(Guid userId)
        {
            var now = DateTime.UtcNow;
            var stats = await _unitOfWork.Properties.GetQueryable()
               .Where(p => p.OwnerID == userId && !p.IsDeleted)
               .Select(p => new {
                   EffectiveStatus = p.ExpiresAt <= now ? PropertyStatus.Expired : p.Status
               })
               .GroupBy(p => p.EffectiveStatus)
               .Select(g => new { Status = g.Key, Count = g.Count() })
               .ToListAsync();

            var result = new PropertyCountStatsDto();
            foreach (var status in Enum.GetNames(typeof(PropertyStatus)))
            {
                result.PropertiesByStatus[status] = 0;
            }
            foreach (var item in stats)
            {
                result.PropertiesByStatus[item.Status.ToString()] = item.Count;
            }
            return Result<PropertyCountStatsDto>.Success(result);
        }


    }
}
